import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'lib/utils/excel_parser.dart';

void main() {
  runApp(const TestExcelImportApp());
}

class TestExcelImportApp extends StatelessWidget {
  const TestExcelImportApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test Excel Import',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const TestExcelImportScreen(),
    );
  }
}

class TestExcelImportScreen extends StatefulWidget {
  const TestExcelImportScreen({super.key});

  @override
  State<TestExcelImportScreen> createState() => _TestExcelImportScreenState();
}

class _TestExcelImportScreenState extends State<TestExcelImportScreen> {
  bool _isLoading = false;
  String? _errorMessage;
  Map<String, dynamic>? _parseResult;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Excel Import'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: <Widget>[
            ElevatedButton(
              onPressed: _pickAndParseFile,
              child: const Text('Pick Excel/CSV File'),
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const CircularProgressIndicator()
            else if (_errorMessage != null)
              Text(
                'Error: $_errorMessage',
                style: const TextStyle(color: Colors.red),
              )
            else if (_parseResult != null)
              Expanded(
                child: _buildResultDisplay(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultDisplay() {
    final List<String> headers = _parseResult!['headers'] as List<String>;
    final List<Map<String, dynamic>> products =
        _parseResult!['products'] as List<Map<String, dynamic>>;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          'Parsed ${products.length} products',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Text('Headers: ${headers.join(', ')}'),
        const SizedBox(height: 16),
        Expanded(
          child: ListView.builder(
            itemCount: products.length,
            itemBuilder: (BuildContext context, int index) {
              final Map<String, dynamic> product = products[index];
              return Card(
                child: ListTile(
                  title: Text(product['الاسم']?.toString() ?? 'No Name'),
                  subtitle: Text(
                    'Unit: ${product['الوحدة'] ?? 'N/A'}, '
                    'Price: ${product['سعر التجزئة'] ?? 'N/A'}',
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Future<void> _pickAndParseFile() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _parseResult = null;
    });

    try {
      final FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: <String>['xlsx', 'xls', 'csv'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final PlatformFile file = result.files.first;
        final Map<String, dynamic> parseResult =
            await ExcelParser.parseExcelFile(file);

        setState(() {
          _parseResult = parseResult;
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }
}
