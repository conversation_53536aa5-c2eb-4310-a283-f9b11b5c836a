/// Model class representing a customer
class Customer {
  /// Unique identifier for the customer
  int? id;

  /// Name of the customer
  String name;

  /// Email address of the customer
  String? email;

  /// Phone number of the customer
  String? phone;

  /// Address of the customer
  String? address;

  /// Balance of the customer (debt)
  double? balance;

  /// Creation date of the customer record
  DateTime? createdAt;

  /// Constructor for creating a Customer instance
  Customer({
    this.id,
    required this.name,
    this.email,
    this.phone,
    this.address,
    this.balance,
    this.createdAt,
  });

  /// Converts the Customer instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'balance': balance,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  /// Creates a Customer instance from a Map (typically from database)
  factory Customer.fromMap(Map<String, dynamic> map) {
    return Customer(
      id: map['id'] as int?,
      name: map['name'] as String? ?? '',
      email: map['email'] as String?,
      phone: map['phone'] as String?,
      address: map['address'] as String?,
      balance: map['balance'] as double?,
      createdAt: map['created_at'] != null
          ? DateTime.tryParse(map['created_at'] as String)
          : null,
    );
  }

  get notes => null;

  @override
  String toString() {
    return 'Customer{id: $id, name: $name, email: $email, '
        'phone: $phone, address: $address}';
  }
}
