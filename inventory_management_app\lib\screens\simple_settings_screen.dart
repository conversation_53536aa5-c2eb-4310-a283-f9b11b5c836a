import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:provider/provider.dart';
import '../providers/enhanced_theme_provider.dart';
import '../providers/product_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/sale_provider.dart';
import '../providers/backup_provider.dart';
import '../services/notification_service.dart';
import 'settings/backup_settings_screen.dart';

class SimpleSettingsScreen extends StatelessWidget {
  const SimpleSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text('الإعدادات'),
          backgroundColor: Colors.grey[700],
          foregroundColor: Colors.white,
        ),
        body: ListView(
          padding: const EdgeInsets.all(16),
          children: <Widget>[
            // إحصائيات سريعة
            _buildStatsCard(context),

            const SizedBox(height: 16),

            // إعدادات التطبيق
            _buildSettingsCard(context),

            const SizedBox(height: 16),

            // معلومات التطبيق
            _buildAboutCard(context),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Text(
              'إحصائيات سريعة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 16),
            Consumer3<ProductProvider, CustomerProvider, SaleProvider>(
              builder: (BuildContext context,
                  ProductProvider productProvider,
                  CustomerProvider customerProvider,
                  SaleProvider saleProvider,
                  Widget? child) {
                final double totalSales = saleProvider.sales.fold<double>(
                  0.0,
                  (double sum, Sale sale) => sum + (sale.totalAmount ?? 0),
                );

                return Row(
                  children: <Widget>[
                    Expanded(
                      child: _buildStatItem(
                        'المنتجات',
                        productProvider.products.length.toString(),
                        Icons.inventory_2,
                        Colors.green,
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        'العملاء',
                        customerProvider.customers.length.toString(),
                        Icons.people,
                        Colors.orange,
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        'المبيعات',
                        saleProvider.sales.length.toString(),
                        Icons.receipt,
                        Colors.purple,
                      ),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),
            Consumer<SaleProvider>(
              builder: (BuildContext context, SaleProvider saleProvider,
                  Widget? child) {
                final double totalSales = saleProvider.sales.fold<double>(
                  0.0,
                  (double sum, Sale sale) => sum + (sale.totalAmount ?? 0),
                );

                return Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: <Widget>[
                      const Text(
                        'إجمالي المبيعات',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${totalSales.toStringAsFixed(2)} ر.س',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: <Widget>[
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Text(
              'إعدادات التطبيق',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            Consumer<NotificationProvider>(
              builder: (BuildContext context,
                  NotificationProvider notificationProvider, Widget? child) {
                return _buildSettingItem(
                  'الإشعارات',
                  'تفعيل الإشعارات',
                  Icons.notifications,
                  notificationProvider.notificationsEnabled,
                  (bool value) {
                    notificationProvider.toggleNotifications(value);
                  },
                );
              },
            ),
            Consumer<EnhancedThemeProvider>(
              builder: (BuildContext context,
                  EnhancedThemeProvider themeProvider, Widget? child) {
                return _buildSettingItem(
                  'الوضع المظلم',
                  'تفعيل الوضع المظلم',
                  Icons.dark_mode,
                  themeProvider.isDarkMode,
                  (bool value) {
                    themeProvider.toggleTheme();
                  },
                );
              },
            ),
            ListTile(
              leading: Icon(Icons.backup, color: Colors.grey[600]),
              title: const Text('النسخ الاحتياطي والاستعادة'),
              subtitle: const Text('إدارة النسخ الاحتياطية المحلية والسحابية'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (BuildContext context) =>
                        const BackupSettingsScreen(),
                  ),
                );
              },
              contentPadding: EdgeInsets.zero,
            ),

            // زر المزامنة السريع
            Consumer<BackupProvider>(
              builder: (BuildContext context, BackupProvider backupProvider,
                  Widget? child) {
                if (!backupProvider.isSignedIn) {
                  return const SizedBox.shrink();
                }

                return ListTile(
                  leading: Icon(
                    Icons.sync,
                    color: backupProvider.canSync
                        ? Colors.orange[600]
                        : Colors.grey[400],
                  ),
                  title: const Text('مزامنة سريعة'),
                  subtitle: Text(
                    backupProvider.isSyncing
                        ? 'جاري المزامنة...'
                        : 'مزامنة مع أحدث نسخة احتياطية',
                  ),
                  trailing: backupProvider.isSyncing
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.arrow_forward_ios),
                  onTap: backupProvider.canSync
                      ? () => backupProvider.performSync(context)
                      : null,
                  contentPadding: EdgeInsets.zero,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingItem(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    Function(bool) onChanged,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.grey[600]),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: Colors.blue,
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildAboutCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Text(
              'حول التطبيق',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            const ListTile(
              leading: Icon(Icons.info, color: Colors.blue),
              title: Text('إصدار التطبيق'),
              subtitle: Text('1.0.0'),
              contentPadding: EdgeInsets.zero,
            ),
            const ListTile(
              leading: Icon(Icons.developer_mode, color: Colors.green),
              title: Text('المطور'),
              subtitle: Text('فريق تطوير إدارة المخزون'),
              contentPadding: EdgeInsets.zero,
            ),
            ListTile(
              leading: const Icon(Icons.email, color: Colors.orange),
              title: const Text('الدعم الفني'),
              subtitle: const Text('<EMAIL>'),
              contentPadding: EdgeInsets.zero,
              onTap: () {
                // TODO: فتح تطبيق البريد الإلكتروني
              },
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  _showClearDataDialog(context);
                },
                icon: const Icon(Icons.delete_forever),
                label: const Text('مسح جميع البيانات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showClearDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: AlertDialog(
            title: const Text('تأكيد المسح'),
            content: const Text(
              'هل أنت متأكد من أنك تريد مسح جميع البيانات؟\n'
              'هذا الإجراء لا يمكن التراجع عنه.',
            ),
            actions: <Widget>[
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  // مسح جميع البيانات
                  context.read<ProductProvider>().clearProducts();
                  context.read<CustomerProvider>().clearCustomers();
                  context.read<SaleProvider>().clearSales();

                  Navigator.of(context).pop();

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم مسح جميع البيانات بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('مسح'),
              ),
            ],
          ),
        );
      },
    );
  }
}
