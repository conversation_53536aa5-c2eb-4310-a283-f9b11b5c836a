import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/app_design_constants.dart';
import '../dashboard/enhanced_dashboard_screen.dart';

/// شاشة الجولة التعريفية للتطبيق
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  int _currentPage = 0;
  final int _totalPages = AppDesignConstants.onboardingPages.length;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: AppDesignConstants.mediumAnimationDuration,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      _pageController.nextPage(
        duration: AppDesignConstants.shortAnimationDuration,
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _skipOnboarding() {
    _completeOnboarding();
  }

  Future<void> _completeOnboarding() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setBool(AppDesignConstants.hasViewedOnboardingKey, true);

      if (!mounted) return;

      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (BuildContext context, Animation<double> animation,
                  Animation<double> secondaryAnimation) =>
              const EnhancedDashboardScreen(),
          transitionsBuilder: (BuildContext context,
              Animation<double> animation,
              Animation<double> secondaryAnimation,
              Widget child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: AppDesignConstants.mediumAnimationDuration,
        ),
      );
    } catch (e) {
      // في حالة حدوث خطأ، انتقل للشاشة الرئيسية
      if (!mounted) return;
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (BuildContext context) => const EnhancedDashboardScreen(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // تعيين شريط الحالة
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppDesignConstants.onboardingGradient,
        ),
        child: SafeArea(
          child: Column(
            children: <Widget>[
              // شريط علوي مع زر التخطي
              _buildTopBar(),

              // محتوى الصفحات
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: (int index) {
                    setState(() {
                      _currentPage = index;
                    });
                  },
                  itemCount: _totalPages,
                  itemBuilder: (BuildContext context, int index) {
                    return _buildOnboardingPage(
                      AppDesignConstants.onboardingPages[index],
                    );
                  },
                ),
              ),

              // مؤشر الصفحات والأزرار
              _buildBottomSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopBar() {
    return Padding(
      padding: const EdgeInsets.all(AppDesignConstants.defaultPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          // شعار التطبيق
          Row(
            children: <Widget>[
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppDesignConstants.primaryColor,
                  borderRadius: BorderRadius.circular(
                    AppDesignConstants.defaultBorderRadius,
                  ),
                ),
                child: const Icon(
                  Icons.store_outlined,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: AppDesignConstants.smallPadding),
              const Text(
                AppDesignConstants.appName,
                style: TextStyle(
                  fontSize: AppDesignConstants.subtitleFontSize,
                  fontWeight: FontWeight.bold,
                  color: AppDesignConstants.primaryColor,
                ),
              ),
            ],
          ),

          // زر التخطي
          if (_currentPage < _totalPages - 1)
            TextButton(
              onPressed: _skipOnboarding,
              child: const Text(
                'تخطي',
                style: TextStyle(
                  fontSize: AppDesignConstants.bodyFontSize,
                  color: AppDesignConstants.textSecondaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildOnboardingPage(OnboardingData data) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Padding(
        padding: const EdgeInsets.all(AppDesignConstants.extraLargePadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            // الأيقونة
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: data.color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(
                  AppDesignConstants.extraLargeBorderRadius,
                ),
              ),
              child: Icon(
                data.icon,
                size: AppDesignConstants.extraLargeIconSize,
                color: data.color,
              ),
            ),

            const SizedBox(height: AppDesignConstants.extraLargePadding),

            // العنوان
            Text(
              data.title,
              style: AppDesignConstants.onboardingTitleStyle,
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: AppDesignConstants.largePadding),

            // الوصف
            Text(
              data.description,
              style: AppDesignConstants.onboardingDescriptionStyle,
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: AppDesignConstants.extraLargePadding),

            // نص تحفيزي
            if (_currentPage < AppDesignConstants.motivationalTexts.length)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDesignConstants.largePadding,
                  vertical: AppDesignConstants.defaultPadding,
                ),
                decoration: BoxDecoration(
                  color: data.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(
                    AppDesignConstants.largeBorderRadius,
                  ),
                ),
                child: Text(
                  AppDesignConstants.motivationalTexts[_currentPage],
                  style: TextStyle(
                    fontSize: AppDesignConstants.bodyFontSize,
                    fontWeight: FontWeight.w600,
                    color: data.color,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomSection() {
    return Padding(
      padding: const EdgeInsets.all(AppDesignConstants.extraLargePadding),
      child: Column(
        children: <Widget>[
          // مؤشر الصفحات
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              _totalPages,
              (int index) => _buildPageIndicator(index),
            ),
          ),

          const SizedBox(height: AppDesignConstants.extraLargePadding),

          // زر التالي أو ابدأ الآن
          SizedBox(
            width: double.infinity,
            height: AppDesignConstants.buttonHeight,
            child: ElevatedButton(
              onPressed: _nextPage,
              style: AppDesignConstants.primaryButtonStyle,
              child: Text(
                _currentPage == _totalPages - 1 ? 'ابدأ الآن' : 'التالي',
                style: const TextStyle(
                  fontSize: AppDesignConstants.subtitleFontSize,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPageIndicator(int index) {
    final bool isActive = index == _currentPage;
    return AnimatedContainer(
      duration: AppDesignConstants.shortAnimationDuration,
      margin: const EdgeInsets.symmetric(
        horizontal: AppDesignConstants.extraSmallPadding,
      ),
      width: isActive ? 24 : 8,
      height: 8,
      decoration: BoxDecoration(
        color: isActive
            ? AppDesignConstants.primaryColor
            : AppDesignConstants.primaryColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}
