import 'package:flutter/material.dart';
import '../../models/supplier.dart';
import 'create_edit_supplier_screen.dart';

class SupplierDetailsScreen extends StatefulWidget {
  final Supplier supplier;

  const SupplierDetailsScreen({
    super.key,
    required this.supplier,
  });

  @override
  State<SupplierDetailsScreen> createState() => _SupplierDetailsScreenState();
}

class _SupplierDetailsScreenState extends State<SupplierDetailsScreen> {
  String _selectedPeriod = 'الشهر';
  final List<String> _periods = <String>[
    'اليوم',
    'الأسبوع',
    'الشهر',
    'السنة',
    'تحديد يدوي'
  ];

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text('تفاصيل المورد'),
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: _editSupplier,
            ),
          ],
        ),
        body: Column(
          children: <Widget>[
            // معلومات المورد الأساسية
            _buildSupplierInfo(),

            // قسم كشف الحساب
            Expanded(
              child: _buildStatementSection(),
            ),
          ],
        ),
        bottomNavigationBar: _buildBottomActions(),
      ),
    );
  }

  Widget _buildSupplierInfo() {
    Color balanceColor = Colors.grey;
    if (widget.supplier.balanceColor == 'green') balanceColor = Colors.green;
    if (widget.supplier.balanceColor == 'red') balanceColor = Colors.red;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // اسم المورد
          Text(
            widget.supplier.name ?? 'مورد غير محدد',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),

          const SizedBox(height: 16),

          // تفاصيل الاتصال
          if (widget.supplier.phone != null) ...<Widget>[
            _buildInfoRow(Icons.phone, 'الهاتف', widget.supplier.phone!),
            const SizedBox(height: 8),
          ],

          if (widget.supplier.email != null) ...<Widget>[
            _buildInfoRow(
                Icons.email, 'البريد الإلكتروني', widget.supplier.email!),
            const SizedBox(height: 8),
          ],

          if (widget.supplier.address != null) ...<Widget>[
            _buildInfoRow(
                Icons.location_on, 'العنوان', widget.supplier.address!),
            const SizedBox(height: 8),
          ],

          // الرصيد الحالي
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: balanceColor.withOpacity(0.3)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                const Text(
                  'الرصيد الحالي:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: <Widget>[
                    Text(
                      widget.supplier.formattedBalance,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: balanceColor,
                      ),
                    ),
                    Text(
                      widget.supplier.balanceStatus,
                      style: TextStyle(
                        fontSize: 12,
                        color: balanceColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: <Widget>[
        Icon(icon, size: 20, color: Colors.green),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.grey,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 16),
          ),
        ),
      ],
    );
  }

  Widget _buildStatementSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // عنوان كشف الحساب
          const Text(
            'كشف الحساب',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          // عوامل تصفية كشف الحساب
          Row(
            children: <Widget>[
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedPeriod,
                  decoration: InputDecoration(
                    labelText: 'الفترة',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  items: _periods.map((String period) {
                    return DropdownMenuItem(
                      value: period,
                      child: Text(period),
                    );
                  }).toList(),
                  onChanged: (String? value) {
                    if (value != null) {
                      setState(() {
                        _selectedPeriod = value;
                      });
                    }
                  },
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton(
                onPressed: _applyFilter,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
                child: const Text('تطبيق'),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // قائمة الحركات
          Expanded(
            child: _buildTransactionsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList() {
    // TODO: تحميل الحركات الفعلية من قاعدة البيانات
    // هذا مثال توضيحي
    final List<Map<String, dynamic>> transactions = <Map<String, dynamic>>[];

    if (transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.receipt_long_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد حركات في هذه الفترة',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: transactions.length,
      itemBuilder: (BuildContext context, int index) {
        final Map<String, dynamic> transaction = transactions[index];
        final bool isCredit = transaction['type'] == 'credit';

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: isCredit ? Colors.green : Colors.red,
              child: Icon(
                isCredit ? Icons.add : Icons.remove,
                color: Colors.white,
              ),
            ),
            title: Text(transaction['description'] as String),
            subtitle: Text(transaction['date'] as String),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: <Widget>[
                Text(
                  '${(transaction['amount'] as double).abs().toStringAsFixed(2)} ر.س',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isCredit ? Colors.green : Colors.red,
                  ),
                ),
                Text(
                  'الرصيد: ${(transaction['balance'] as double).toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: <Widget>[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _printStatement,
              icon: const Icon(Icons.print),
              label: const Text('طباعة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _saveAsPdf,
              icon: const Icon(Icons.picture_as_pdf),
              label: const Text('PDF'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _shareStatement,
              icon: const Icon(Icons.share),
              label: const Text('مشاركة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _recordPayment,
              icon: const Icon(Icons.payments),
              label: const Text('دفعة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _editSupplier() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) =>
            CreateEditSupplierScreen(supplier: widget.supplier),
      ),
    );
  }

  void _applyFilter() {
    // TODO: تطبيق التصفية على كشف الحساب
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير التصفية قريباً')),
    );
  }

  void _printStatement() {
    // TODO: طباعة كشف الحساب
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير الطباعة قريباً')),
    );
  }

  void _saveAsPdf() {
    // TODO: حفظ كشف الحساب كـ PDF
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير حفظ PDF قريباً')),
    );
  }

  void _shareStatement() {
    // TODO: مشاركة كشف الحساب
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير المشاركة قريباً')),
    );
  }

  void _recordPayment() {
    // TODO: تسجيل دفعة للمورد
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير تسجيل الدفعات قريباً')),
    );
  }
}
