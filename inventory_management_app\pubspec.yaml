name: inventory_management_app
description: A Flutter app for managing inventory with SQLite integration and state management.

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  provider: ^6.0.0
  sqflite: ^2.0.0+4
  path: ^1.8.0
  go_router: ^14.2.7
  shared_preferences: ^2.2.2
  file_picker: ^8.0.0
  sqflite_common_ffi: ^2.0.0
  sqflite_common_ffi_web: ^1.0.0
  fl_chart: ^0.66.0
  permission_handler: ^11.3.1
  path_provider: ^2.1.2
  intl: ^0.20.0
  google_fonts: ^6.1.0
  crypto: ^3.0.3
  http: ^1.1.0
  url_launcher: ^6.2.0
  image_picker: ^1.0.0
  connectivity_plus: ^5.0.0
  device_info_plus: ^9.1.2
  package_info_plus: ^4.2.0
  # contacts_service: ^0.6.3  # معلق مؤقتاً - غير متوافق مع Dart 3
  excel: ^4.0.0
  google_sign_in: ^6.1.6
  googleapis: ^11.4.0
  workmanager: ^0.6.0
  animated_text_kit: ^4.2.2
  flutter_spinkit: ^5.2.0
  pdf: ^3.10.7
  printing: ^5.12.0
  share_plus: ^7.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf