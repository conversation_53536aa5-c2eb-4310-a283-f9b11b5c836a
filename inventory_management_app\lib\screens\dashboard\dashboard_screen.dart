import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../config/app_colors.dart';
import '../../widgets/dashboard_widgets.dart';
import '../../widgets/bottom_navigation.dart';
import '../../providers/product_provider.dart';
import '../../providers/sale_provider.dart';
import '../../providers/purchase_provider.dart';
import '../../providers/customer_provider.dart';
import '../../providers/supplier_provider.dart';

/// الشاشة الرئيسية للتطبيق
class DashboardScreen extends StatefulWidget {
  /// Constructor for DashboardScreen
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<ProductProvider>().fetchProducts();
        context.read<SaleProvider>().fetchSales();
        context.read<PurchaseProvider>().fetchPurchases();
        context.read<CustomerProvider>().fetchCustomers();
        context.read<SupplierProvider>().fetchSuppliers();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('إدارة محل المواد الغذائية'),
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.notifications_outlined),
              onPressed: () {
                // TODO: إضافة شاشة الإشعارات
              },
            ),
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () {
                // TODO: إضافة البحث العام
              },
            ),
          ],
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            _loadData();
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                // ترحيب
                _buildWelcomeSection(),
                const SizedBox(height: 24),

                // الإحصائيات الرئيسية
                _buildStatsSection(),
                const SizedBox(height: 24),

                // الإجراءات السريعة
                _buildQuickActionsSection(),
                const SizedBox(height: 24),

                // التنبيهات والإشعارات
                _buildAlertsSection(),
                const SizedBox(height: 24),

                // النشاط الأخير
                _buildRecentActivitySection(),
              ],
            ),
          ),
        ),
        bottomNavigationBar: CustomBottomNavigation(
          currentIndex: BottomNavigationHelper.getCurrentIndex('/'),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: <Widget>[
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const Text(
                  'مرحباً بك',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textOnPrimary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'إدارة محل المواد الغذائية',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColors.textOnPrimary.withOpacity(0.9),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'اليوم: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textOnPrimary.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.store,
              color: AppColors.textOnPrimary,
              size: 32,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          'الإحصائيات',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        Consumer5<ProductProvider, SaleProvider, PurchaseProvider,
            CustomerProvider, SupplierProvider>(
          builder: (BuildContext context,
              ProductProvider productProvider,
              SaleProvider saleProvider,
              PurchaseProvider purchaseProvider,
              CustomerProvider customerProvider,
              SupplierProvider supplierProvider,
              Widget? child) {
            return GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              childAspectRatio: 1.2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: <Widget>[
                StatCard(
                  title: 'إجمالي المنتجات',
                  value: '${productProvider.products.length}',
                  icon: Icons.inventory_2,
                  color: AppColors.primary,
                  subtitle: 'منتج متاح',
                  onTap: () => context.go('/products'),
                  isLoading: productProvider.isLoading,
                ),
                StatCard(
                  title: 'العملاء',
                  value: '${customerProvider.customers.length}',
                  icon: Icons.people,
                  color: AppColors.secondary,
                  subtitle: 'عميل مسجل',
                  onTap: () => context.go('/customers'),
                  isLoading: customerProvider.isLoading,
                ),
                StatCard(
                  title: 'المبيعات اليوم',
                  value: '${saleProvider.sales.length}',
                  icon: Icons.point_of_sale,
                  color: AppColors.accent,
                  subtitle: 'عملية بيع',
                  onTap: () => context.go('/sales'),
                  isLoading: saleProvider.isLoading,
                ),
                StatCard(
                  title: 'الموردين',
                  value: '${supplierProvider.suppliers.length}',
                  icon: Icons.local_shipping,
                  color: AppColors.info,
                  subtitle: 'مورد نشط',
                  onTap: () => context.go('/suppliers'),
                  isLoading: supplierProvider.isLoading,
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildQuickActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 3,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 0.9,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          children: <Widget>[
            QuickActionCard(
              title: 'إضافة منتج',
              subtitle: 'منتج جديد',
              icon: Icons.add_box,
              color: AppColors.primary,
              onTap: () => context.go('/products/add'),
            ),
            QuickActionCard(
              title: 'عملية بيع',
              subtitle: 'بيع جديد',
              icon: Icons.point_of_sale,
              color: AppColors.accent,
              onTap: () => context.go('/sales/add'),
            ),
            QuickActionCard(
              title: 'عملية شراء',
              subtitle: 'شراء جديد',
              icon: Icons.shopping_cart,
              color: AppColors.secondary,
              onTap: () => context.go('/purchases/add'),
            ),
            QuickActionCard(
              title: 'عميل جديد',
              subtitle: 'إضافة عميل',
              icon: Icons.person_add,
              color: AppColors.info,
              onTap: () => context.go('/customers/add'),
            ),
            QuickActionCard(
              title: 'التقارير',
              subtitle: 'عرض التقارير',
              icon: Icons.analytics,
              color: AppColors.warning,
              onTap: () => context.go('/reports'),
            ),
            QuickActionCard(
              title: 'الإعدادات',
              subtitle: 'إعدادات التطبيق',
              icon: Icons.settings,
              color: AppColors.textSecondary,
              onTap: () => context.go('/settings'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAlertsSection() {
    return Consumer<ProductProvider>(
      builder: (BuildContext context, ProductProvider productProvider,
          Widget? child) {
        // حساب المنتجات منخفضة المخزون
        final List<Product> lowStockProducts = productProvider.products
            .where((Product product) => (product.quantity ?? 0) < 10)
            .toList();

        if (lowStockProducts.isEmpty) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Text(
              'التنبيهات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            AlertCard(
              title: 'تنبيه مخزون منخفض',
              message: '${lowStockProducts.length} منتج يحتاج إعادة تخزين',
              icon: Icons.warning,
              color: AppColors.warning,
              onTap: () => context.go('/products'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildRecentActivitySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            const Text(
              'النشاط الأخير',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            TextButton(
              onPressed: () => context.go('/activities'),
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: <Widget>[
                ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.add_box,
                      color: AppColors.primary,
                      size: 20,
                    ),
                  ),
                  title: const Text('تم إضافة منتج جديد'),
                  subtitle: const Text('منذ ساعتين'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                ),
                const Divider(),
                ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.accent.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.point_of_sale,
                      color: AppColors.accent,
                      size: 20,
                    ),
                  ),
                  title: const Text('عملية بيع جديدة'),
                  subtitle: const Text('منذ 3 ساعات'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
