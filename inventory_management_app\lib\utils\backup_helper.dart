import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../config/app_constants.dart';
import '../utils/date_helper.dart';
import '../utils/settings_helper.dart';
import '../data/database_helper.dart'; // Ensure this path is correct and DatabaseHelper is defined here

/// فئة مساعدة للنسخ الاحتياطي والاستعادة
class BackupHelper {
  /// إنشاء نسخة احتياطية كاملة
  static Future<BackupResult> createFullBackup() async {
    try {
      final Map<String, dynamic> backupData = await _collectAllData();
      final String fileName = _generateBackupFileName();
      final String filePath = await _saveBackupFile(fileName, backupData);

      // تحديث تاريخ آخر نسخة احتياطية
      await SettingsHelper.setLastBackupDate(DateTime.now());

      return BackupResult(
        success: true,
        filePath: filePath,
        fileName: fileName,
        size: await _getFileSize(filePath),
        message: 'تم إنشاء النسخة الاحتياطية بنجاح',
      );
    } catch (e) {
      return BackupResult(
        success: false,
        message: 'فشل في إنشاء النسخة الاحتياطية: $e',
      );
    }
  }

  /// إنشاء نسخة احتياطية لجدول محدد
  static Future<BackupResult> createTableBackup(String tableName) async {
    try {
      final db = await DatabaseHelper.database;
      final data = await db.query(tableName);

      final Map<String, dynamic> backupData = <String, dynamic>{
        'backupDate': DateTime.now().toIso8601String(),
        'appName': AppConstants.appName,
        'version': AppConstants.appVersion,
        'type': 'table',
        'tableName': tableName,
        'data': data,
      };

      final String fileName = _generateTableBackupFileName(tableName);
      final String filePath = await _saveBackupFile(fileName, backupData);

      return BackupResult(
        success: true,
        filePath: filePath,
        fileName: fileName,
        size: await _getFileSize(filePath),
        message: 'تم إنشاء نسخة احتياطية لجدول $tableName',
      );
    } catch (e) {
      return BackupResult(
        success: false,
        message: 'فشل في إنشاء نسخة احتياطية للجدول: $e',
      );
    }
  }

  /// استعادة النسخة الاحتياطية
  static Future<RestoreResult> restoreBackup(String filePath) async {
    try {
      final Map<String, dynamic> backupData = await _loadBackupFile(filePath);

      if (!_validateBackupData(backupData)) {
        return RestoreResult(
          success: false,
          message: 'ملف النسخة الاحتياطية غير صالح',
        );
      }

      final List<String> restoredTables = await _restoreData(backupData);

      return RestoreResult(
        success: true,
        restoredTables: restoredTables,
        message: 'تم استعادة النسخة الاحتياطية بنجاح',
      );
    } catch (e) {
      return RestoreResult(
        success: false,
        message: 'فشل في استعادة النسخة الاحتياطية: $e',
      );
    }
  }

  /// الحصول على قائمة النسخ الاحتياطية المتاحة
  static Future<List<BackupInfo>> getAvailableBackups() async {
    try {
      final String backupDir = await _getBackupDirectory();
      final Directory directory = Directory(backupDir);

      if (!await directory.exists()) {
        return <BackupInfo>[];
      }

      final List<FileSystemEntity> entities = await directory.list().toList();
      final List<File> files = entities
          .whereType<File>()
          .where((File file) => file.path.endsWith('.backup'))
          .toList();

      final List<BackupInfo> backups = <BackupInfo>[];

      for (final File file in files) {
        try {
          final FileStat stat = await file.stat();
          final Map<String, dynamic> backupData =
              await _loadBackupFile(file.path);

          backups.add(BackupInfo(
            fileName: file.path.split('/').last,
            filePath: file.path,
            size: stat.size,
            createdAt: DateTime.parse(backupData['backupDate']),
            type: backupData['type'] ?? 'full',
            appVersion: backupData['version'] ?? 'غير معروف',
            tableCount: _getTableCount(backupData),
          ));
        } catch (e) {
          // تجاهل الملفات التالفة
          continue;
        }
      }

      // ترتيب حسب التاريخ (الأحدث أولاً)
      backups.sort(
          (BackupInfo a, BackupInfo b) => b.createdAt.compareTo(a.createdAt));

      return backups;
    } catch (e) {
      return <BackupInfo>[];
    }
  }

  /// حذف النسخة الاحتياطية
  static Future<bool> deleteBackup(String filePath) async {
    try {
      final File file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// تنظيف النسخ الاحتياطية القديمة
  static Future<int> cleanOldBackups({int daysToKeep = 30}) async {
    try {
      final List<BackupInfo> backups = await getAvailableBackups();
      final DateTime cutoffDate =
          DateTime.now().subtract(Duration(days: daysToKeep));
      int deletedCount = 0;

      for (final BackupInfo backup in backups) {
        if (backup.createdAt.isBefore(cutoffDate)) {
          if (await deleteBackup(backup.filePath)) {
            deletedCount++;
          }
        }
      }

      return deletedCount;
    } catch (e) {
      return 0;
    }
  }

  /// التحقق من ضرورة إنشاء نسخة احتياطية تلقائية
  static bool shouldCreateAutoBackup() {
    if (!SettingsHelper.getAutoBackup()) return false;

    final DateTime? lastBackup = SettingsHelper.getLastBackupDate();
    if (lastBackup == null) return true;

    final String frequency = SettingsHelper.getBackupFrequency();
    final DateTime now = DateTime.now();

    switch (frequency) {
      case 'daily':
        return now.difference(lastBackup).inDays >= 1;
      case 'weekly':
        return now.difference(lastBackup).inDays >= 7;
      case 'monthly':
        return now.difference(lastBackup).inDays >= 30;
      default:
        return false;
    }
  }

  /// تصدير النسخة الاحتياطية إلى مجلد خارجي
  static Future<String?> exportBackup(
      String backupPath, String exportPath) async {
    try {
      final File sourceFile = File(backupPath);
      final File targetFile = File(exportPath);

      await sourceFile.copy(targetFile.path);
      return targetFile.path;
    } catch (e) {
      return null;
    }
  }

  /// استيراد نسخة احتياطية من مجلد خارجي
  static Future<String?> importBackup(String externalPath) async {
    try {
      final File sourceFile = File(externalPath);
      final String backupDir = await _getBackupDirectory();
      final String fileName = sourceFile.path.split('/').last;
      final String targetPath = '$backupDir/$fileName';

      await sourceFile.copy(targetPath);
      return targetPath;
    } catch (e) {
      return null;
    }
  }

  /// جمع جميع البيانات من قاعدة البيانات
  static Future<Map<String, dynamic>> _collectAllData() async {
    final db = await DatabaseHelper.database;

    final List<String> tables = <String>[
      AppConstants.productsTable,
      AppConstants.customersTable,
      AppConstants.suppliersTable,
      AppConstants.salesTable,
      AppConstants.saleItemsTable,
      AppConstants.purchasesTable,
      AppConstants.purchaseItemsTable,
      AppConstants.expensesTable,
    ];

    final Map<String, List<Map<String, dynamic>>> data =
        <String, List<Map<String, dynamic>>>{};

    for (final String table in tables) {
      try {
        data[table] = await db.query(table);
      } catch (e) {
        // تجاهل الجداول غير الموجودة
        data[table] = <Map<String, dynamic>>[];
      }
    }

    return <String, dynamic>{
      'backupDate': DateTime.now().toIso8601String(),
      'appName': AppConstants.appName,
      'version': AppConstants.appVersion,
      'type': 'full',
      'data': data,
      'settings': SettingsHelper.exportSettings(),
    };
  }

  /// حفظ ملف النسخة الاحتياطية
  static Future<String> _saveBackupFile(
      String fileName, Map<String, dynamic> data) async {
    final String backupDir = await _getBackupDirectory();
    final Directory directory = Directory(backupDir);

    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }

    final File file = File('$backupDir/$fileName');
    final String jsonString = json.encode(data);
    await file.writeAsString(jsonString);

    return file.path;
  }

  /// تحميل ملف النسخة الاحتياطية
  static Future<Map<String, dynamic>> _loadBackupFile(String filePath) async {
    final File file = File(filePath);
    final String jsonString = await file.readAsString();
    return json.decode(jsonString) as Map<String, dynamic>;
  }

  /// التحقق من صحة بيانات النسخة الاحتياطية
  static bool _validateBackupData(Map<String, dynamic> data) {
    return data.containsKey('backupDate') &&
        data.containsKey('appName') &&
        data.containsKey('data');
  }

  /// استعادة البيانات إلى قاعدة البيانات
  static Future<List<String>> _restoreData(
      Map<String, dynamic> backupData) async {
    final db = await DatabaseHelper.database;
    final Map<String, dynamic> data =
        backupData['data'] as Map<String, dynamic>;
    final List<String> restoredTables = <String>[];

    // بدء معاملة قاعدة البيانات
    await db.transaction((txn) async {
      for (final MapEntry<String, dynamic> entry in data.entries) {
        final String tableName = entry.key;
        final List tableData = entry.value as List;

        try {
          // حذف البيانات الموجودة
          await txn.delete(tableName);

          // إدراج البيانات المستعادة
          for (final row in tableData) {
            await txn.insert(tableName, row as Map<String, dynamic>);
          }

          restoredTables.add(tableName);
        } catch (e) {
          // تجاهل أخطاء الجداول غير الموجودة
          continue;
        }
      }
    });

    // استعادة الإعدادات إذا كانت متوفرة
    if (backupData.containsKey('settings')) {
      try {
        await SettingsHelper.importSettings(
          backupData['settings'] as Map<String, dynamic>,
        );
      } catch (e) {
        // تجاهل أخطاء استعادة الإعدادات
      }
    }

    return restoredTables;
  }

  /// الحصول على مجلد النسخ الاحتياطية
  static Future<String> _getBackupDirectory() async {
    if (kIsWeb) {
      return 'backups'; // مجلد افتراضي للويب
    }

    final Directory appDir = await getApplicationDocumentsDirectory();
    return '${appDir.path}/${AppConstants.backupsFolder}';
  }

  /// إنشاء اسم ملف النسخة الاحتياطية
  static String _generateBackupFileName() {
    final String timestamp = DateHelper.formatDate(
      DateTime.now(),
      format: AppConstants.backupDateFormat,
    );
    return '${AppConstants.appName}_backup_$timestamp${AppConstants.backupFileExtension}';
  }

  /// إنشاء اسم ملف نسخة احتياطية لجدول
  static String _generateTableBackupFileName(String tableName) {
    final String timestamp = DateHelper.formatDate(
      DateTime.now(),
      format: AppConstants.backupDateFormat,
    );
    return '${AppConstants.appName}_${tableName}_$timestamp${AppConstants.backupFileExtension}';
  }

  /// الحصول على حجم الملف
  static Future<int> _getFileSize(String filePath) async {
    try {
      final File file = File(filePath);
      final FileStat stat = await file.stat();
      return stat.size;
    } catch (e) {
      return 0;
    }
  }

  /// الحصول على عدد الجداول في النسخة الاحتياطية
  static int _getTableCount(Map<String, dynamic> backupData) {
    if (backupData['type'] == 'table') {
      return 1;
    }

    final Map<String, dynamic>? data =
        backupData['data'] as Map<String, dynamic>?;
    return data?.length ?? 0;
  }
}

/// نتيجة عملية النسخ الاحتياطي
class BackupResult {
  final bool success;
  final String? filePath;
  final String? fileName;
  final int? size;
  final String message;

  BackupResult({
    required this.success,
    this.filePath,
    this.fileName,
    this.size,
    required this.message,
  });
}

/// نتيجة عملية الاستعادة
class RestoreResult {
  final bool success;
  final List<String>? restoredTables;
  final String message;

  RestoreResult({
    required this.success,
    this.restoredTables,
    required this.message,
  });
}

/// معلومات النسخة الاحتياطية
class BackupInfo {
  final String fileName;
  final String filePath;
  final int size;
  final DateTime createdAt;
  final String type;
  final String appVersion;
  final int tableCount;

  BackupInfo({
    required this.fileName,
    required this.filePath,
    required this.size,
    required this.createdAt,
    required this.type,
    required this.appVersion,
    required this.tableCount,
  });

  String get formattedSize {
    if (size < 1024) {
      return '$size بايت';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)} كيلوبايت';
    } else {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    }
  }

  String get formattedDate {
    return DateHelper.formatDateTime(createdAt);
  }

  String get typeDisplayName {
    switch (type) {
      case 'full':
        return 'نسخة كاملة';
      case 'table':
        return 'نسخة جدول';
      default:
        return type;
    }
  }
}
