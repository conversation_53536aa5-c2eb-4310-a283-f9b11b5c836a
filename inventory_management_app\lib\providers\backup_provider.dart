import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import '../services/backup_service.dart';
import '../services/database_service.dart';

import 'sync_provider.dart';
import 'auto_backup_provider.dart';
import '../models/enums.dart';

/// مزود النسخ الاحتياطي والاستعادة
class BackupProvider extends ChangeNotifier {
  final BackupService _backupService = BackupService();
  final DatabaseService _databaseService = DatabaseService();

  // المزودات المساعدة
  late final SyncProvider _syncProvider;
  late final AutoBackupProvider _autoBackupProvider;

  // حالة تسجيل الدخول
  GoogleSignInAccount? _currentUser;
  bool _isSignedIn = false;

  // قوائم النسخ الاحتياطية
  List<drive.File> _driveBackups = <drive.File>[];
  List<File> _localBackups = <File>[];

  // حالة التحميل والأخطاء
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  // Getters
  GoogleSignInAccount? get currentUser => _currentUser;
  bool get isSignedIn => _isSignedIn;
  List<drive.File> get driveBackups => _driveBackups;
  List<File> get localBackups => _localBackups;
  bool get isLoading => _isLoading;
  String? get error => _errorMessage;
  String? get errorMessage => _errorMessage;
  String? get successMessage => _successMessage;

  // Getters للمزودات المساعدة
  SyncProvider get syncProvider => _syncProvider;

  // Getters مفوضة للمزودات المساعدة
  bool get isSyncing => _syncProvider.isSyncing;
  DateTime? get lastSyncTime => _syncProvider.lastSyncTime;
  String? get lastSyncStatus => _syncProvider.lastSyncStatus;
  String get lastSyncStatusText => _syncProvider.lastSyncStatusText;
  bool get canSync => _isSignedIn && !_isLoading && !_syncProvider.isSyncing;

  /// حالة تفعيل النسخ الاحتياطي التلقائي
  bool get autoBackupEnabled => _autoBackupProvider.autoBackupEnabled;

  /// تردد النسخ الاحتياطي التلقائي
  BackupFrequency get autoBackupFrequency =>
      _autoBackupProvider.autoBackupFrequency;

  /// حالة النسخ الاحتياطي للسحابة
  bool get autoBackupToCloud => _autoBackupProvider.autoBackupToCloud;

  /// تهيئة المزود
  Future<void> initialize() async {
    try {
      // تهيئة المزودات المساعدة
      _syncProvider = SyncProvider();
      _autoBackupProvider = AutoBackupProvider();
      await _autoBackupProvider.initialize();

      _currentUser = _backupService.currentUser;
      _isSignedIn = _backupService.isSignedIn;

      if (_isSignedIn) {
        await refreshDriveBackups();
      }

      await refreshLocalBackups();
      notifyListeners();
    } catch (e) {
      _setError('فشل في تهيئة مزود النسخ الاحتياطي: $e');
    }
  }

  // ==================== دوال المزامنة المفوضة ====================

  /// تنفيذ المزامنة مع Google Drive
  Future<void> performSync(BuildContext context) async {
    await _syncProvider.performSync(context, _driveBackups);
    notifyListeners();
  }

  /// إعادة تعيين حالة المزامنة
  void resetSyncStatus() {
    _syncProvider.resetSyncStatus();
    notifyListeners();
  }

  // ==================== دوال النسخ الاحتياطي التلقائي ====================

  /// تفعيل/تعطيل النسخ الاحتياطي التلقائي
  Future<void> setAutoBackupEnabled(bool enabled) async {
    await _autoBackupProvider.setAutoBackupEnabled(enabled);
    notifyListeners();
  }

  /// تفعيل/تعطيل النسخ الاحتياطي التلقائي للسحابة
  Future<void> setAutoBackupToCloud(bool enabled) async {
    await _autoBackupProvider.setAutoBackupToCloud(enabled);
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _setSuccess(String? message) {
    _successMessage = message;
    notifyListeners();
  }

  void _clearMessages() {
    _errorMessage = null;
    _successMessage = null;
  }

  void clearMessages() {
    _errorMessage = null;
    _successMessage = null;
    notifyListeners();
  }

  /// تسجيل الدخول إلى Google
  Future<void> signInGoogle() async {
    try {
      _setLoading(true);
      _clearMessages();

      final GoogleSignInAccount? account =
          await _backupService.signInWithGoogle();

      if (account != null) {
        _currentUser = account;
        _isSignedIn = true;
        _setSuccess('تم تسجيل الدخول بنجاح: ${account.email}');
        await refreshDriveBackups();
      } else {
        _setError('تم إلغاء تسجيل الدخول');
      }
    } catch (e) {
      _setError('فشل في تسجيل الدخول: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تسجيل الخروج من Google
  Future<void> signOutGoogle() async {
    try {
      _setLoading(true);
      _clearMessages();

      await _backupService.signOutGoogle();
      _currentUser = null;
      _isSignedIn = false;
      _driveBackups.clear();

      _setSuccess('تم تسجيل الخروج بنجاح');
    } catch (e) {
      _setError('فشل في تسجيل الخروج: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// إنشاء نسخة احتياطية محلية
  Future<void> performLocalBackup() async {
    try {
      _setLoading(true);
      _clearMessages();

      final String backupPath =
          await _backupService.createLocalDatabaseBackup();
      await refreshLocalBackups();

      // تنظيف النسخ القديمة
      await _backupService.cleanOldLocalDatabaseBackups();

      _setSuccess('تم إنشاء النسخة الاحتياطية المحلية بنجاح');
      debugPrint('تم إنشاء النسخة الاحتياطية في: $backupPath');
    } catch (e) {
      _setError('فشل في إنشاء النسخة الاحتياطية المحلية: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Create local backup of the database (legacy method for compatibility)
  Future<bool> createLocalBackup() async {
    await performLocalBackup();
    return _errorMessage == null;
  }

  /// استعادة من نسخة احتياطية محلية
  Future<void> restoreLocalBackup(File backupFile) async {
    try {
      _setLoading(true);
      _clearMessages();

      await _databaseService.restoreDatabaseFromFile(backupFile.path);
      _setSuccess('تم استعادة البيانات من النسخة الاحتياطية المحلية بنجاح');
    } catch (e) {
      _setError('فشل في استعادة النسخة الاحتياطية المحلية: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Restore database from backup file (legacy method for compatibility) - محذوف
  // Future<bool> restoreLocalBackup() async {
  //   try {
  //     _setLoading(true);
  //     _clearMessages();
  //
  //     // This method is kept for compatibility but should use file picker
  //     _setError('يرجى استخدام قائمة النسخ الاحتياطية لاختيار ملف للاستعادة');
  //     return false;
  //   } catch (e) {
  //     _setError('فشل في استعادة النسخة الاحتياطية: $e');
  //     return false;
  //   } finally {
  //     _setLoading(false);
  //   }
  // }

  /// إنشاء نسخة احتياطية على Google Drive
  Future<void> performGoogleDriveBackup() async {
    try {
      if (!_isSignedIn) {
        throw Exception('يجب تسجيل الدخول إلى Google أولاً');
      }

      _setLoading(true);
      _clearMessages();

      final String? fileId = await _backupService.uploadDatabaseToGoogleDrive();

      if (fileId != null) {
        await refreshDriveBackups();
        _setSuccess('تم رفع النسخة الاحتياطية إلى Google Drive بنجاح');
      } else {
        throw Exception('فشل في رفع الملف');
      }
    } catch (e) {
      _setError('فشل في إنشاء النسخة الاحتياطية على Google Drive: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// استعادة من Google Drive
  Future<void> restoreFromGoogleDrive(drive.File driveFile) async {
    try {
      if (!_isSignedIn) {
        throw Exception('يجب تسجيل الدخول إلى Google أولاً');
      }

      _setLoading(true);
      _clearMessages();

      await _backupService.downloadDatabaseFromGoogleDrive(driveFile.id!);
      _setSuccess('تم استعادة البيانات من Google Drive بنجاح');
    } catch (e) {
      _setError('فشل في استعادة البيانات من Google Drive: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث قائمة النسخ الاحتياطية على Google Drive
  Future<void> refreshDriveBackups() async {
    try {
      if (!_isSignedIn) return;

      _driveBackups = await _backupService.listBackupFilesOnDrive();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث قائمة النسخ الاحتياطية على Drive: $e');
    }
  }

  /// تحديث قائمة النسخ الاحتياطية المحلية
  Future<void> refreshLocalBackups() async {
    try {
      _localBackups = await _backupService.listLocalDatabaseBackups();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث قائمة النسخ الاحتياطية المحلية: $e');
    }
  }

  /// حذف نسخة احتياطية من Google Drive
  Future<void> deleteFromGoogleDrive(String fileId) async {
    try {
      _setLoading(true);
      _clearMessages();

      await _backupService.deleteFileFromGoogleDrive(fileId);
      await refreshDriveBackups();

      _setSuccess('تم حذف النسخة الاحتياطية من Google Drive');
    } catch (e) {
      _setError('فشل في حذف النسخة الاحتياطية: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// حذف نسخة احتياطية محلية
  Future<void> deleteLocalBackup(File backupFile) async {
    try {
      await backupFile.delete();
      await refreshLocalBackups();
      _setSuccess('تم حذف النسخة الاحتياطية المحلية');
    } catch (e) {
      _setError('فشل في حذف النسخة الاحتياطية المحلية: $e');
    }
  }

  // تفعيل/تعطيل النسخ الاحتياطي التلقائي (التعريف الثاني - محذوف)
  // Future<void> setAutoBackupEnabled(bool enabled) async {
  //   _autoBackupEnabled = enabled;
  //   await _saveSettings();
  //
  //   if (enabled) {
  //     await _scheduleAutoBackup();
  //   } else {
  //     await _cancelAutoBackup();
  //   }
  //
  //   notifyListeners();
  // }

  /// تغيير تردد النسخ الاحتياطي التلقائي
  Future<void> setAutoBackupFrequency(BackupFrequency frequency) async {
    // تفويض للـ AutoBackupProvider
    await _autoBackupProvider.setAutoBackupFrequency(frequency);
    notifyListeners();
  }

  // تفعيل/تعطيل النسخ الاحتياطي التلقائي للسحابة (التعريف الثاني - محذوف)
  // Future<void> setAutoBackupToCloud(bool enabled) async {
  //   _autoBackupToCloud = enabled;
  //   await _saveSettings();
  //   notifyListeners();
  // }

  // جدولة النسخ الاحتياطي التلقائي - تم نقلها إلى AutoBackupProvider
  // Future<void> _scheduleAutoBackup() async { ... }

  // إلغاء النسخ الاحتياطي التلقائي - تم نقلها إلى AutoBackupProvider
  // Future<void> _cancelAutoBackup() async { ... }

  /// Get list of available local backups (legacy method for compatibility)
  Future<List<FileSystemEntity>> getLocalBackups() async {
    await refreshLocalBackups();
    return _localBackups.cast<FileSystemEntity>();
  }

  /// Delete a backup file (legacy method for compatibility)
  Future<bool> deleteBackup(String backupPath) async {
    try {
      final File file = File(backupPath);
      await deleteLocalBackup(file);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get backup file info
  Map<String, dynamic> getBackupInfo(FileSystemEntity backup) {
    final File file = backup as File;
    final String fileName = file.path.split('/').last;
    final DateTime lastModified = file.lastModifiedSync();
    final int size = file.lengthSync();

    return <String, dynamic>{
      'name': fileName,
      'path': file.path,
      'lastModified': lastModified,
      'size': size,
      'sizeFormatted': _backupService.formatFileSize(size),
    };
  }

  /// Get drive file info
  Map<String, dynamic> getDriveFileInfo(drive.File driveFile) {
    return <String, dynamic>{
      'name': driveFile.name ?? 'Unknown',
      'id': driveFile.id ?? '',
      'modifiedTime': driveFile.modifiedTime,
      'size': driveFile.size != null ? int.parse(driveFile.size!) : 0,
      'sizeFormatted': driveFile.size != null
          ? _backupService.formatFileSize(int.parse(driveFile.size!))
          : 'Unknown',
    };
  }

  // ==================== دوال مساعدة ====================
  // (الدوال المساعدة موجودة في بداية الملف - تم حذف التكرار)
}
