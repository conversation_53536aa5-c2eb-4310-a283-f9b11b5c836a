import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/purchase_provider.dart';
import 'package:inventory_management_app/models/purchase.dart';
import 'package:inventory_management_app/models/purchase_item.dart';
import 'package:intl/intl.dart';

class PurchaseDetailsScreen extends StatefulWidget {
  final Purchase? purchase;

  const PurchaseDetailsScreen({super.key, this.purchase});

  @override
  _PurchaseDetailsScreenState createState() => _PurchaseDetailsScreenState();
}

class _PurchaseDetailsScreenState extends State<PurchaseDetailsScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late int _supplierId;
  late DateTime? _purchaseDate;
  late double? _totalAmount;
  late String? _notes;

  @override
  void initState() {
    super.initState();
    _supplierId = widget.purchase?.supplierId ?? 0;
    _purchaseDate = widget.purchase?.purchaseDate;
    _totalAmount = widget.purchase?.totalAmount;
    _notes = widget.purchase?.notes ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.purchase == null ? 'Add Purchase' : 'Edit Purchase'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: <Widget>[
              TextFormField(
                initialValue: _supplierId.toString(),
                decoration: const InputDecoration(labelText: 'Supplier ID'),
                keyboardType: TextInputType.number,
                validator: (String? value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a Supplier ID';
                  }
                  return null;
                },
                onSaved: (String? value) => _supplierId = int.parse(value!),
              ),
              TextFormField(
                initialValue: _purchaseDate != null
                    ? DateFormat('yyyy-MM-dd').format(_purchaseDate!)
                    : null,
                decoration: const InputDecoration(labelText: 'Purchase Date'),
                onTap: () async {
                  DateTime? pickedDate = await showDatePicker(
                      context: context,
                      initialDate:
                          widget.purchase?.purchaseDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime(2101));
                  setState(() {
                    _purchaseDate = pickedDate;
                  });
                },
                readOnly: true,
                onSaved: (String? value) => _purchaseDate =
                    value != null ? DateTime.parse(value) : null,
              ),
              TextFormField(
                initialValue: _totalAmount?.toString(),
                decoration: const InputDecoration(labelText: 'Total Amount'),
                keyboardType: TextInputType.number,
                onSaved: (String? value) =>
                    _totalAmount = double.tryParse(value!),
              ),
              TextFormField(
                initialValue: _notes,
                decoration: const InputDecoration(labelText: 'Notes'),
                onSaved: (String? value) => _notes = value,
              ),
              ElevatedButton(
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    _formKey.currentState!.save();
                    final Purchase purchase = Purchase(
                      id: widget.purchase?.id,
                      supplierId: _supplierId,
                      purchaseDate: _purchaseDate,
                      totalAmount: _totalAmount,
                      notes: _notes,
                    );
                    final PurchaseProvider purchaseProvider =
                        Provider.of<PurchaseProvider>(context, listen: false);
                    if (widget.purchase == null) {
                      purchaseProvider.addPurchase(purchase, <PurchaseItem>[]); // Replace with actual list of PurchaseItem(s)
                    } else {
                      purchaseProvider.updatePurchase(purchase, <PurchaseItem>[]); // Replace with actual list of PurchaseItem(s)
                    }
                    Navigator.pop(context);
                  }
                },
                child: const Text('Save'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
