import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/sale_provider.dart';
import '../../models/sale.dart';

/// شاشة تفاصيل البيع
class SaleDetailsScreen extends StatefulWidget {
  final int? saleId;
  final bool isReadOnly;

  const SaleDetailsScreen({
    super.key,
    this.saleId,
    this.isReadOnly = false,
  });

  @override
  State<SaleDetailsScreen> createState() => _SaleDetailsScreenState();
}

class _SaleDetailsScreenState extends State<SaleDetailsScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late double _total;
  Sale? _sale;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSale();
  }

  void _loadSale() {
    if (widget.saleId != null) {
      setState(() {
        _isLoading = true;
      });

      final SaleProvider saleProvider = context.read<SaleProvider>();
      _sale = saleProvider.sales
          .where((Sale s) => s.id == widget.saleId)
          .firstOrNull;

      _total = _sale?.total ?? 0.0;

      setState(() {
        _isLoading = false;
      });
    } else {
      _total = 0.0;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: Text(widget.saleId == null
              ? 'إضافة بيع جديد'
              : widget.isReadOnly
                  ? 'تفاصيل البيع'
                  : 'تعديل البيع'),
          backgroundColor: Colors.purple,
          foregroundColor: Colors.white,
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              children: <Widget>[
                TextFormField(
                  initialValue: _total.toString(),
                  readOnly: widget.isReadOnly,
                  decoration: const InputDecoration(
                    labelText: 'المبلغ الإجمالي',
                    suffixText: 'ر.س',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (String? value) {
                    if (!widget.isReadOnly &&
                        (value == null || value.isEmpty)) {
                      return 'يرجى إدخال المبلغ الإجمالي';
                    }
                    return null;
                  },
                  onSaved: (String? value) => _total = double.parse(value!),
                ),
                const SizedBox(height: 16),
                if (widget.isReadOnly && _sale != null) ...<Widget>[
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text('رقم البيع: ${_sale!.id}'),
                          Text('التاريخ: ${_sale!.date ?? 'غير محدد'}'),
                          Text(
                              'المبلغ الإجمالي: ${_sale!.total?.toStringAsFixed(2) ?? '0.00'} ر.س'),
                        ],
                      ),
                    ),
                  ),
                ],
                if (!widget.isReadOnly)
                  ElevatedButton(
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        _formKey.currentState!.save();
                        final Sale sale = Sale(
                          id: _sale?.id,
                          total: _total,
                          date: DateTime.now().toIso8601String(),
                        );
                        final SaleProvider saleProvider =
                            Provider.of<SaleProvider>(context, listen: false);
                        if (widget.saleId == null) {
                          saleProvider.addSale(sale);
                        } else {
                          saleProvider.updateSale(sale);
                        }
                        Navigator.pop(context);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                    child: Text(widget.saleId == null ? 'إضافة' : 'حفظ'),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
