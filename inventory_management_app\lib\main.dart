import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/backup_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:workmanager/workmanager.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:sqflite/sqflite.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:inventory_management_app/providers/product_provider.dart';
import 'package:inventory_management_app/providers/customer_provider.dart';
import 'package:inventory_management_app/providers/supplier_provider.dart';
import 'package:inventory_management_app/providers/purchase_provider.dart';
import 'package:inventory_management_app/providers/order_provider.dart';
import 'package:inventory_management_app/providers/expense_provider.dart';
import 'package:inventory_management_app/providers/notification_provider.dart';
import 'package:inventory_management_app/providers/backup_provider.dart';
import 'package:inventory_management_app/providers/auto_backup_provider.dart';
import 'package:inventory_management_app/providers/sync_provider.dart';

import 'package:inventory_management_app/providers/customer_statement_provider.dart';
import 'package:inventory_management_app/providers/supplier_statement_provider.dart';
import 'package:inventory_management_app/providers/activity_provider.dart';
import 'package:inventory_management_app/providers/sale_provider.dart';
import 'package:inventory_management_app/providers/enhanced_theme_provider.dart';
import 'package:inventory_management_app/providers/internal_transfer_provider.dart';
import 'package:inventory_management_app/providers/analytics_provider.dart';
import 'package:inventory_management_app/providers/store_inventory_provider.dart';
import 'package:inventory_management_app/providers/validation_provider.dart';
import 'package:inventory_management_app/services/database_service.dart';
import 'package:inventory_management_app/services/backup_service.dart';

import 'package:inventory_management_app/screens/products/product_list_screen.dart';
import 'package:inventory_management_app/screens/customers/customer_list_screen.dart';
import 'package:inventory_management_app/screens/suppliers/supplier_list_screen.dart';
import 'package:inventory_management_app/screens/settings/settings_screen.dart';
import 'package:inventory_management_app/screens/dashboard/dashboard_screen.dart';
import 'package:inventory_management_app/screens/splash/splash_screen.dart';
import 'package:inventory_management_app/config/app_colors.dart';
import 'package:inventory_management_app/config/app_styles.dart';
import 'package:inventory_management_app/config/app_dimensions.dart';
import 'package:inventory_management_app/core/app_router.dart';

/// Workmanager callback for background tasks
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager()
      .executeTask((String task, Map<String, dynamic>? inputData) async {
    try {
      print('🔄 Executing background task: $task');

      switch (task) {
        case 'autoBackupTask':
          await _performAutoBackup(inputData);
          break;
        default:
          print('❌ Unknown task: $task');
          return Future.value(false);
      }

      print('✅ Background task completed: $task');
      return Future.value(true);
    } catch (e) {
      print('❌ Background task failed: $task - $e');
      return Future.value(false);
    }
  });
}

/// Perform automatic backup
Future<void> _performAutoBackup(Map<String, dynamic>? inputData) async {
  try {
    final BackupService backupService = BackupService();

    // Create local backup
    await backupService.createLocalDatabaseBackup();
    print('✅ Auto backup: Local backup created');

    // Upload to cloud if enabled
    final bool backupToCloud = inputData?['backup_to_cloud'] ?? false;
    if (backupToCloud && backupService.isSignedIn) {
      await backupService.uploadDatabaseToGoogleDrive();
      print('✅ Auto backup: Cloud backup uploaded');
    }

    // Clean old backups
    await backupService.cleanOldLocalDatabaseBackups();
    print('✅ Auto backup: Old backups cleaned');
  } catch (e) {
    print('❌ Auto backup failed: $e');
    rethrow;
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Workmanager
  try {
    await Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: kDebugMode,
    );
    print('✅ Workmanager initialized successfully');
  } catch (e) {
    print('❌ Workmanager initialization error: $e');
  }

  // Initialize database
  try {
    // Initialize database factory for web
    if (kIsWeb) {
      databaseFactory = databaseFactoryFfiWeb;
    }

    await DatabaseService.instance.database;
    print('✅ Database initialized successfully');
  } catch (e) {
    print('❌ Database initialization error: $e');
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => BackupProvider()..initialize(),
        ),
      ],
      child: MaterialApp(
        title: 'أسامة ماركت - إدارة ذكية لمتجرك',
        debugShowCheckedModeBanner: false,
        theme: _buildLightTheme(),
        darkTheme: _buildDarkTheme(),
        themeMode: ThemeMode.light,
        home: const SplashScreen(),
        builder: (context, child) {
          return Directionality(
            textDirection: TextDirection.rtl,
            child: child ?? const SizedBox(),
          );
        },
      ),
    );
  }

  /// بناء الثيم الفاتح
  static ThemeData _buildLightTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.light,
        primary: AppColors.primary,
        secondary: AppColors.accent,
        surface: AppColors.surface,
        error: AppColors.error,
      ),
      textTheme: TextTheme(
        headlineLarge: AppStyles.headlineLarge,
        headlineMedium: AppStyles.headlineMedium,
        headlineSmall: AppStyles.headlineSmall,
        titleLarge: AppStyles.titleLarge,
        titleMedium: AppStyles.titleMedium,
        titleSmall: AppStyles.titleSmall,
        bodyLarge: AppStyles.bodyLarge,
        bodyMedium: AppStyles.bodyMedium,
        bodySmall: AppStyles.bodySmall,
        labelLarge: AppStyles.labelLarge,
        labelMedium: AppStyles.labelMedium,
        labelSmall: AppStyles.labelSmall,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        elevation: AppDimensions.elevationM,
        centerTitle: true,
        titleTextStyle: AppStyles.titleLarge.copyWith(
          color: AppColors.textOnPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
          elevation: AppDimensions.elevationM,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingL,
            vertical: AppDimensions.paddingM,
          ),
          minimumSize: const Size(double.infinity, AppDimensions.buttonHeight),
        ),
      ),
      cardTheme: CardThemeData(
        color: AppColors.surface,
        elevation: AppDimensions.elevationS,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          borderSide: const BorderSide(color: AppColors.divider),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          borderSide: const BorderSide(color: AppColors.divider),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          borderSide: const BorderSide(color: AppColors.error),
        ),
        filled: true,
        fillColor: AppColors.surface,
        contentPadding: const EdgeInsets.all(AppDimensions.paddingM),
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textSecondary,
        backgroundColor: AppColors.surface,
        elevation: AppDimensions.elevationM,
      ),
    );
  }

  /// بناء الثيم الداكن
  static ThemeData _buildDarkTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.dark,
      ),
      // يمكن إضافة المزيد من التخصيصات للثيم الداكن هنا
    );
  }
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  Widget _getSelectedWidget() {
    try {
      switch (_selectedIndex) {
        case 0:
          return _buildHomeTab();
        case 1:
          return _buildProductsTab();
        case 2:
          return _buildCustomersTab();
        case 3:
          return _buildSuppliersTab();
        case 4:
          return _buildSettingsTab();
        default:
          return _buildHomeTab();
      }
    } catch (e, s) {
      debugPrint('❌ خطأ في التبويب $_selectedIndex: $e');
      debugPrint('Stack trace: $s');
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'حدث خطأ في تحميل التبويب',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'تفاصيل الخطأ: $e',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _selectedIndex = 0; // العودة للصفحة الرئيسية
                  });
                },
                icon: const Icon(Icons.home),
                label: const Text('العودة للرئيسية'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildHomeTab() {
    try {
      return const DashboardScreen();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الصفحة الرئيسية: $e');
      return _buildErrorWidget('الصفحة الرئيسية', e.toString());
    }
  }

  Widget _buildProductsTab() {
    try {
      return const ProductListScreen();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل شاشة المنتجات: $e');
      return _buildErrorWidget('المنتجات', e.toString());
    }
  }

  Widget _buildCustomersTab() {
    try {
      return const CustomerListScreen();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل شاشة العملاء: $e');
      return _buildErrorWidget('العملاء', e.toString());
    }
  }

  Widget _buildSuppliersTab() {
    try {
      return const SupplierListScreen();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل شاشة الموردين: $e');
      return _buildErrorWidget('الموردين', e.toString());
    }
  }

  Widget _buildSettingsTab() {
    try {
      return const SettingsScreen();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل شاشة الإعدادات: $e');
      return _buildErrorWidget('الإعدادات', e.toString());
    }
  }

  Widget _buildErrorWidget(String screenName, String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل شاشة $screenName',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  // إعادة بناء الشاشة
                });
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          title: Text(
            'أسامة ماركت',
            style: AppStyles.titleLarge.copyWith(
              color: AppColors.textOnPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
          elevation: AppDimensions.elevationM,
          centerTitle: true,
        ),
        body: _getSelectedWidget(),
        bottomNavigationBar: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          selectedItemColor: AppColors.primary,
          unselectedItemColor: AppColors.textSecondary,
          backgroundColor: AppColors.surface,
          elevation: AppDimensions.elevationM,
          currentIndex: _selectedIndex,
          onTap: _onItemTapped,
          selectedLabelStyle: AppStyles.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: AppStyles.labelMedium,
          items: const <BottomNavigationBarItem>[
            BottomNavigationBarItem(
              icon: Icon(Icons.home_outlined),
              activeIcon: Icon(Icons.home),
              label: 'الرئيسية',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.inventory_2_outlined),
              activeIcon: Icon(Icons.inventory_2),
              label: 'المنتجات',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.people_outline),
              activeIcon: Icon(Icons.people),
              label: 'العملاء',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.local_shipping_outlined),
              activeIcon: Icon(Icons.local_shipping),
              label: 'الموردون',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.settings_outlined),
              activeIcon: Icon(Icons.settings),
              label: 'الإعدادات',
            ),
          ],
        ),
      ),
    );
  }
}
