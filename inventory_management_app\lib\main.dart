import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart';
import 'package:workmanager/workmanager.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:sqflite/sqflite.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:inventory_management_app/providers/product_provider.dart';
import 'package:inventory_management_app/providers/customer_provider.dart';
import 'package:inventory_management_app/providers/supplier_provider.dart';
import 'package:inventory_management_app/providers/purchase_provider.dart';
import 'package:inventory_management_app/providers/order_provider.dart';
import 'package:inventory_management_app/providers/expense_provider.dart';
import 'package:inventory_management_app/providers/notification_provider.dart';
import 'package:inventory_management_app/providers/backup_provider.dart';
import 'package:inventory_management_app/providers/sync_provider.dart';

import 'package:inventory_management_app/providers/customer_statement_provider.dart';
import 'package:inventory_management_app/providers/supplier_statement_provider.dart';
import 'package:inventory_management_app/providers/activity_provider.dart';
import 'package:inventory_management_app/providers/sale_provider.dart';
import 'package:inventory_management_app/providers/enhanced_theme_provider.dart';
import 'package:inventory_management_app/providers/internal_transfer_provider.dart';
import 'package:inventory_management_app/providers/analytics_provider.dart';
import 'package:inventory_management_app/providers/store_inventory_provider.dart';
import 'package:inventory_management_app/providers/validation_provider.dart';
import 'package:inventory_management_app/services/database_service.dart';
import 'package:inventory_management_app/services/backup_service.dart';

import 'package:inventory_management_app/screens/products/enhanced_product_list_screen.dart';
import 'package:inventory_management_app/screens/customers/enhanced_customer_list_screen.dart';
import 'package:inventory_management_app/screens/suppliers/supplier_list_screen.dart';
import 'package:inventory_management_app/screens/settings/enhanced_settings_screen.dart';
import 'package:inventory_management_app/screens/enhanced_dashboard_screen.dart';
import 'package:inventory_management_app/config/app_colors.dart';
import 'package:inventory_management_app/config/app_styles.dart';
import 'package:inventory_management_app/config/app_dimensions.dart';

/// Workmanager callback for background tasks
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager()
      .executeTask((String task, Map<String, dynamic>? inputData) async {
    try {
      print('🔄 Executing background task: $task');

      switch (task) {
        case 'autoBackupTask':
          await _performAutoBackup(inputData);
          break;
        default:
          print('❌ Unknown task: $task');
          return Future.value(false);
      }

      print('✅ Background task completed: $task');
      return Future.value(true);
    } catch (e) {
      print('❌ Background task failed: $task - $e');
      return Future.value(false);
    }
  });
}

/// Perform automatic backup
Future<void> _performAutoBackup(Map<String, dynamic>? inputData) async {
  try {
    final BackupService backupService = BackupService();

    // Create local backup
    await backupService.createLocalDatabaseBackup();
    print('✅ Auto backup: Local backup created');

    // Upload to cloud if enabled
    final bool backupToCloud = inputData?['backup_to_cloud'] ?? false;
    if (backupToCloud && backupService.isSignedIn) {
      await backupService.uploadDatabaseToGoogleDrive();
      print('✅ Auto backup: Cloud backup uploaded');
    }

    // Clean old backups
    await backupService.cleanOldLocalDatabaseBackups();
    print('✅ Auto backup: Old backups cleaned');
  } catch (e) {
    print('❌ Auto backup failed: $e');
    rethrow;
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Workmanager
  try {
    await Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: kDebugMode,
    );
    print('✅ Workmanager initialized successfully');
  } catch (e) {
    print('❌ Workmanager initialization error: $e');
  }

  // Initialize database
  try {
    // Initialize database factory for web
    if (kIsWeb) {
      databaseFactory = databaseFactoryFfiWeb;
    }

    await DatabaseService.instance.database;
    print('✅ Database initialized successfully');
  } catch (e) {
    print('❌ Database initialization error: $e');
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: <ChangeNotifierProvider<dynamic>>[
        ChangeNotifierProvider(
          create: (_) => EnhancedThemeProvider()..initialize(),
        ),
        ChangeNotifierProvider(
          create: (_) => SyncProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => ProductProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => CustomerProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => SupplierProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => PurchaseProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => OrderProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => ExpenseProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => NotificationProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => BackupProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => CustomerStatementProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => SupplierStatementProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => ActivityProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => SaleProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => InternalTransferProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => AnalyticsProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => StoreInventoryProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => ValidationProvider(),
        ),
      ],
      child: Consumer<EnhancedThemeProvider>(
        builder: (BuildContext context, EnhancedThemeProvider themeProvider,
            Widget? child) {
          return MaterialApp(
            title: 'أسامة ماركت - إدارة ذكية لمتجرك',
            debugShowCheckedModeBanner: false,
            theme: _buildLightTheme(),
            darkTheme: _buildDarkTheme(),
            themeMode: themeProvider.themeMode,
            home: const SplashScreen(),
          );
        },
      ),
    );
  }

  /// بناء الثيم الفاتح
  static ThemeData _buildLightTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.light,
        primary: AppColors.primary,
        secondary: AppColors.accent,
        surface: AppColors.surface,
        error: AppColors.error,
      ),
      textTheme: TextTheme(
        headlineLarge: AppStyles.headlineLarge,
        headlineMedium: AppStyles.headlineMedium,
        headlineSmall: AppStyles.headlineSmall,
        titleLarge: AppStyles.titleLarge,
        titleMedium: AppStyles.titleMedium,
        titleSmall: AppStyles.titleSmall,
        bodyLarge: AppStyles.bodyLarge,
        bodyMedium: AppStyles.bodyMedium,
        bodySmall: AppStyles.bodySmall,
        labelLarge: AppStyles.labelLarge,
        labelMedium: AppStyles.labelMedium,
        labelSmall: AppStyles.labelSmall,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        elevation: AppDimensions.elevationM,
        centerTitle: true,
        titleTextStyle: AppStyles.titleLarge.copyWith(
          color: AppColors.textOnPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
          elevation: AppDimensions.elevationM,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingL,
            vertical: AppDimensions.paddingM,
          ),
          minimumSize: const Size(double.infinity, AppDimensions.buttonHeight),
        ),
      ),
      cardTheme: CardThemeData(
        color: AppColors.surface,
        elevation: AppDimensions.elevationS,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          borderSide: const BorderSide(color: AppColors.divider),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          borderSide: const BorderSide(color: AppColors.divider),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          borderSide: const BorderSide(color: AppColors.error),
        ),
        filled: true,
        fillColor: AppColors.surface,
        contentPadding: const EdgeInsets.all(AppDimensions.paddingM),
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textSecondary,
        backgroundColor: AppColors.surface,
        elevation: AppDimensions.elevationM,
      ),
    );
  }

  /// بناء الثيم الداكن
  static ThemeData _buildDarkTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.dark,
      ),
      // يمكن إضافة المزيد من التخصيصات للثيم الداكن هنا
    );
  }
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  Widget _getSelectedWidget() {
    try {
      switch (_selectedIndex) {
        case 0:
          return _buildHomeTab();
        case 1:
          return _buildProductsTab();
        case 2:
          return _buildCustomersTab();
        case 3:
          return _buildSuppliersTab();
        case 4:
          return _buildSettingsTab();
        default:
          return _buildHomeTab();
      }
    } catch (e, s) {
      debugPrint('❌ خطأ في التبويب $_selectedIndex: $e');
      debugPrint('Stack trace: $s');
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'حدث خطأ في تحميل التبويب',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'تفاصيل الخطأ: $e',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _selectedIndex = 0; // العودة للصفحة الرئيسية
                  });
                },
                icon: const Icon(Icons.home),
                label: const Text('العودة للرئيسية'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildHomeTab() {
    try {
      return const EnhancedDashboardScreen();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الصفحة الرئيسية: $e');
      return _buildErrorWidget('الصفحة الرئيسية', e.toString());
    }
  }

  Widget _buildProductsTab() {
    try {
      return const EnhancedProductListScreen();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل شاشة المنتجات: $e');
      return _buildErrorWidget('المنتجات', e.toString());
    }
  }

  Widget _buildCustomersTab() {
    try {
      return const EnhancedCustomerListScreen();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل شاشة العملاء: $e');
      return _buildErrorWidget('العملاء', e.toString());
    }
  }

  Widget _buildSuppliersTab() {
    try {
      return const SupplierListScreen();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل شاشة الموردين: $e');
      return _buildErrorWidget('الموردين', e.toString());
    }
  }

  Widget _buildSettingsTab() {
    try {
      return const EnhancedSettingsScreen();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل شاشة الإعدادات: $e');
      return _buildErrorWidget('الإعدادات', e.toString());
    }
  }

  Widget _buildErrorWidget(String screenName, String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل شاشة $screenName',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  // إعادة بناء الشاشة
                });
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          title: Text(
            'أسامة ماركت',
            style: AppStyles.titleLarge.copyWith(
              color: AppColors.textOnPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
          elevation: AppDimensions.elevationM,
          centerTitle: true,
        ),
        body: _getSelectedWidget(),
        bottomNavigationBar: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          selectedItemColor: AppColors.primary,
          unselectedItemColor: AppColors.textSecondary,
          backgroundColor: AppColors.surface,
          elevation: AppDimensions.elevationM,
          currentIndex: _selectedIndex,
          onTap: _onItemTapped,
          selectedLabelStyle: AppStyles.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: AppStyles.labelMedium,
          items: const <BottomNavigationBarItem>[
            BottomNavigationBarItem(
              icon: Icon(Icons.home_outlined),
              activeIcon: Icon(Icons.home),
              label: 'الرئيسية',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.inventory_2_outlined),
              activeIcon: Icon(Icons.inventory_2),
              label: 'المنتجات',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.people_outline),
              activeIcon: Icon(Icons.people),
              label: 'العملاء',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.local_shipping_outlined),
              activeIcon: Icon(Icons.local_shipping),
              label: 'الموردون',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.settings_outlined),
              activeIcon: Icon(Icons.settings),
              label: 'الإعدادات',
            ),
          ],
        ),
      ),
    );
  }
}

/// شاشة البداية مع الرسوم المتحركة
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startSplash();
  }

  void _initAnimations() {
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _controller.forward();
  }

  void _startSplash() async {
    await Future.delayed(const Duration(seconds: 3));

    if (mounted) {
      // فحص إذا كان المستخدم شاهد الـ onboarding من قبل
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final bool hasViewedOnboarding =
          prefs.getBool('has_viewed_onboarding') ?? false;

      if (hasViewedOnboarding) {
        // انتقال للشاشة الرئيسية
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
              builder: (BuildContext context) => const HomeScreen()),
        );
      } else {
        // انتقال لشاشة الـ onboarding
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
              builder: (BuildContext context) => const OnboardingScreen()),
        );
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: <Color>[
              Color(0xFFE3F2FD),
              Color(0xFFFFFFFF),
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              // الشعار المتحرك
              AnimatedBuilder(
                animation: _controller,
                builder: (BuildContext context, Widget? child) {
                  return FadeTransition(
                    opacity: _fadeAnimation,
                    child: ScaleTransition(
                      scale: _scaleAnimation,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: const Color(0xFF1976D2),
                          borderRadius: BorderRadius.circular(30),
                          boxShadow: <BoxShadow>[
                            BoxShadow(
                              color: Colors.blue.withValues(alpha: 0.3),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.store_outlined,
                          size: 70,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 40),

              // اسم التطبيق
              FadeTransition(
                opacity: _fadeAnimation,
                child: const Text(
                  'أسامة ماركت',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1976D2),
                    letterSpacing: 1.5,
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // الشعار
              FadeTransition(
                opacity: _fadeAnimation,
                child: const Text(
                  'إدارة ذكية لمتجرك',
                  style: TextStyle(
                    fontSize: 18,
                    color: Color(0xFF757575),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

              const SizedBox(height: 60),

              // مؤشر التحميل
              FadeTransition(
                opacity: _fadeAnimation,
                child: const Column(
                  children: <Widget>[
                    SizedBox(
                      width: 30,
                      height: 30,
                      child: CircularProgressIndicator(
                        strokeWidth: 3,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Color(0xFF1976D2),
                        ),
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'جاري التحميل...',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF757575),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// شاشة التعريف بالتطبيق
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingPage> _pages = <OnboardingPage>[
    OnboardingPage(
      title: 'إدارة المخزون بذكاء',
      description:
          'تتبع كل منتج في المخزن والمتجر، وتلقي تنبيهات بالمخزون المنخفض',
      icon: Icons.inventory_2_outlined,
      color: const Color(0xFF1976D2),
    ),
    OnboardingPage(
      title: 'تتبع المبيعات والأرباح',
      description: 'راقب مبيعاتك اليومية والشهرية، واحسب أرباحك بدقة',
      icon: Icons.trending_up_outlined,
      color: const Color(0xFF4CAF50),
    ),
    OnboardingPage(
      title: 'إدارة العملاء والموردين',
      description: 'احتفظ بسجل كامل لعملائك وموردينك، وتتبع المدفوعات والديون',
      icon: Icons.people_outline,
      color: const Color(0xFF00BCD4),
    ),
    OnboardingPage(
      title: 'تقارير وإحصائيات شاملة',
      description: 'احصل على رؤى عميقة لأداء متجرك مع تقارير مفصلة',
      icon: Icons.analytics_outlined,
      color: const Color(0xFFFF9800),
    ),
  ];

  void _completeOnboarding() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool('has_viewed_onboarding', true);

    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
            builder: (BuildContext context) => const HomeScreen()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: <Widget>[
            // شريط علوي
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  const Text(
                    'أسامة ماركت',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1976D2),
                    ),
                  ),
                  if (_currentPage < _pages.length - 1)
                    TextButton(
                      onPressed: _completeOnboarding,
                      child: const Text('تخطي'),
                    ),
                ],
              ),
            ),

            // محتوى الصفحات
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (int index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _pages.length,
                itemBuilder: (BuildContext context, int index) {
                  final OnboardingPage page = _pages[index];
                  return Padding(
                    padding: const EdgeInsets.all(32),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            color: page.color.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(30),
                          ),
                          child: Icon(
                            page.icon,
                            size: 60,
                            color: page.color,
                          ),
                        ),
                        const SizedBox(height: 40),
                        Text(
                          page.title,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 20),
                        Text(
                          page.description,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                            height: 1.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

            // مؤشر الصفحات والزر
            Padding(
              padding: const EdgeInsets.all(32),
              child: Column(
                children: <Widget>[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      _pages.length,
                      (int index) => Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        width: _currentPage == index ? 24 : 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: _currentPage == index
                              ? const Color(0xFF1976D2)
                              : Colors.grey.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 32),
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: () {
                        if (_currentPage < _pages.length - 1) {
                          _pageController.nextPage(
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          );
                        } else {
                          _completeOnboarding();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF1976D2),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        _currentPage == _pages.length - 1
                            ? 'ابدأ الآن'
                            : 'التالي',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// نموذج صفحة التعريف
class OnboardingPage {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  OnboardingPage({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}
