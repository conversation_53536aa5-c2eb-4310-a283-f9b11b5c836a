import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';

import 'package:inventory_management_app/screens/onboarding/onboarding_screen.dart';
// import 'package:inventory_management_app/screens/dashboard/modern_dashboard_screen.dart';
import 'package:inventory_management_app/screens/dashboard/dashboard_screen.dart';
import 'package:inventory_management_app/screens/products/product_list_screen.dart';
import 'package:inventory_management_app/screens/products/product_form_screen.dart';
import 'package:inventory_management_app/screens/products/product_details_screen.dart';
import 'package:inventory_management_app/screens/customers/customer_list_screen.dart';
import 'package:inventory_management_app/screens/customers/customer_form_screen.dart';
import 'package:inventory_management_app/screens/sales/sale_list_screen.dart';
import 'package:inventory_management_app/screens/reports/reports_screen.dart';
import 'package:inventory_management_app/screens/settings/settings_screen.dart';
import 'package:inventory_management_app/screens/customers/customers_screen.dart';
import 'package:inventory_management_app/screens/suppliers/suppliers_screen.dart';
import 'package:inventory_management_app/screens/orders/orders_screen.dart';
import 'package:inventory_management_app/screens/sales/sales_screen.dart';
import 'package:inventory_management_app/screens/purchases/purchases_screen.dart';
import 'package:inventory_management_app/screens/expenses/expenses_screen.dart';
import 'package:inventory_management_app/screens/categories/categories_screen.dart';
import 'package:inventory_management_app/screens/units/units_screen.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    routes: <RouteBase>[
      GoRoute(
        path: '/',
        builder: (BuildContext context, GoRouterState state) =>
            const DashboardScreen(),
      ),
      GoRoute(
        path: '/products',
        builder: (BuildContext context, GoRouterState state) =>
            const ProductListScreen(),
      ),
      GoRoute(
        path: '/products/add',
        builder: (BuildContext context, GoRouterState state) =>
            const ProductFormScreen(),
      ),
      GoRoute(
        path: '/products/edit/:id',
        builder: (BuildContext context, GoRouterState state) =>
            ProductFormScreen(
          productId: state.pathParameters['id'],
          isEditing: true,
        ),
      ),
      GoRoute(
        path: '/products/details/:id',
        builder: (BuildContext context, GoRouterState state) =>
            ProductDetailsScreen(
          productId: state.pathParameters['id']!,
        ),
      ),
      GoRoute(
        path: '/customers',
        builder: (BuildContext context, GoRouterState state) =>
            const EnhancedCustomerListScreen(),
      ),
      GoRoute(
        path: '/customers/add',
        builder: (BuildContext context, GoRouterState state) =>
            const EnhancedCustomerFormScreen(),
      ),
      GoRoute(
        path: '/customers/edit/:id',
        builder: (BuildContext context, GoRouterState state) =>
            EnhancedCustomerFormScreen(
          customerId: state.pathParameters['id'],
          isEditing: true,
        ),
      ),
      GoRoute(
        path: '/suppliers',
        builder: (BuildContext context, GoRouterState state) =>
            const SuppliersScreen(),
      ),
      GoRoute(
        path: '/orders',
        builder: (BuildContext context, GoRouterState state) =>
            const OrdersScreen(),
      ),
      GoRoute(
        path: '/sales',
        builder: (BuildContext context, GoRouterState state) =>
            const EnhancedSaleListScreen(),
      ),
      GoRoute(
        path: '/purchases',
        builder: (BuildContext context, GoRouterState state) =>
            const PurchasesScreen(),
      ),
      GoRoute(
        path: '/expenses',
        builder: (BuildContext context, GoRouterState state) =>
            const ExpensesScreen(),
      ),
      GoRoute(
        path: '/categories',
        builder: (BuildContext context, GoRouterState state) =>
            const CategoriesScreen(),
      ),
      GoRoute(
        path: '/units',
        builder: (BuildContext context, GoRouterState state) =>
            const UnitsScreen(),
      ),
      GoRoute(
        // GoRoute(
        //   path: '/backup',
        //   builder: (BuildContext context, GoRouterState state) => BackupScreen(),
        // ),
        path: '/reports',
        builder: (BuildContext context, GoRouterState state) =>
            const EnhancedReportsScreen(),
      ),
      GoRoute(
        path: '/settings',
        builder: (BuildContext context, GoRouterState state) =>
            const EnhancedSettingsScreen(),
      ),
    ],
  );
}
