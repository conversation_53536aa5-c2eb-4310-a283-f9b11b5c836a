import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';

// شاشات التطبيق الرئيسية
import '../screens/dashboard/dashboard_screen.dart';
import '../screens/splash/splash_screen.dart';

// شاشات المنتجات
import '../screens/products/product_list_screen.dart';
import '../screens/products/product_form_screen.dart';
import '../screens/products/product_details_screen.dart';

// شاشات العملاء
import '../screens/customers/customer_list_screen.dart';
import '../screens/customers/customer_form_screen.dart';

// شاشات الموردين
import '../screens/suppliers/suppliers_screen.dart';

// شاشات الطلبات
import '../screens/orders/orders_screen.dart';

// شاشات المبيعات
import '../screens/sales/sale_list_screen.dart';

// شاشات المشتريات
import '../screens/purchases/purchases_screen.dart';

// شاشات المصروفات
import '../screens/expenses/expenses_screen.dart';

// شاشات الفئات والوحدات
import '../screens/categories/categories_screen.dart';
import '../screens/units/units_screen.dart';

// شاشات التقارير والإعدادات
import '../screens/reports/reports_screen.dart';
import '../screens/settings/settings_screen.dart';

/// فئة إدارة التوجيه في التطبيق
/// تحتوي على جميع المسارات والشاشات المتاحة
class AppRouter {
  /// كائن GoRouter الرئيسي للتطبيق
  static final GoRouter router = GoRouter(
    initialLocation: '/splash',
    routes: <RouteBase>[
      GoRoute(
        path: '/splash',
        builder: (BuildContext context, GoRouterState state) =>
            const SplashScreen(),
      ),
      GoRoute(
        path: '/',
        builder: (BuildContext context, GoRouterState state) =>
            const DashboardScreen(),
      ),
      GoRoute(
        path: '/products',
        builder: (BuildContext context, GoRouterState state) =>
            const ProductListScreen(),
      ),
      GoRoute(
        path: '/products/add',
        builder: (BuildContext context, GoRouterState state) =>
            const ProductFormScreen(),
      ),
      GoRoute(
        path: '/products/edit/:id',
        builder: (BuildContext context, GoRouterState state) {
          final String? productId = state.pathParameters['id'];
          return ProductFormScreen(
            productId: productId,
            isEditing: true,
          );
        },
      ),
      GoRoute(
        path: '/products/details/:id',
        builder: (BuildContext context, GoRouterState state) {
          final String productId = state.pathParameters['id'] ?? '';
          return ProductDetailsScreen(
            productId: productId,
          );
        },
      ),
      GoRoute(
        path: '/customers',
        builder: (BuildContext context, GoRouterState state) =>
            const CustomerListScreen(),
      ),
      GoRoute(
        path: '/customers/add',
        builder: (BuildContext context, GoRouterState state) =>
            const CustomerFormScreen(),
      ),
      GoRoute(
        path: '/customers/edit/:id',
        builder: (BuildContext context, GoRouterState state) {
          final String? customerId = state.pathParameters['id'];
          return CustomerFormScreen(
            customerId: customerId,
            isEditing: true,
          );
        },
      ),
      GoRoute(
        path: '/suppliers',
        builder: (BuildContext context, GoRouterState state) =>
            const SuppliersScreen(),
      ),
      GoRoute(
        path: '/orders',
        builder: (BuildContext context, GoRouterState state) =>
            const OrdersScreen(),
      ),
      GoRoute(
        path: '/sales',
        builder: (BuildContext context, GoRouterState state) =>
            const SaleListScreen(),
      ),
      GoRoute(
        path: '/purchases',
        builder: (BuildContext context, GoRouterState state) =>
            const PurchasesScreen(),
      ),
      GoRoute(
        path: '/expenses',
        builder: (BuildContext context, GoRouterState state) =>
            const ExpensesScreen(),
      ),
      GoRoute(
        path: '/categories',
        builder: (BuildContext context, GoRouterState state) =>
            const CategoriesScreen(),
      ),
      GoRoute(
        path: '/units',
        builder: (BuildContext context, GoRouterState state) =>
            const UnitsScreen(),
      ),
      // TODO: إضافة شاشة النسخ الاحتياطي لاحقاً
      // GoRoute(
      //   path: '/backup',
      //   builder: (BuildContext context, GoRouterState state) => BackupScreen(),
      // ),
      GoRoute(
        path: '/reports',
        builder: (BuildContext context, GoRouterState state) =>
            const ReportsScreen(),
      ),
      GoRoute(
        path: '/settings',
        builder: (BuildContext context, GoRouterState state) =>
            const SettingsScreen(),
      ),
    ],
  );
}
